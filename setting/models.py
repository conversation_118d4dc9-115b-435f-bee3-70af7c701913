import json
from django.db import models
from django.conf import settings
from devproject.utils.azure_storage import AzureBlobStorage

class SystemSettings(models.Model):
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    value_type = models.CharField(max_length=20, default='text', choices=[
        ('text', 'Text Value'),
        ('image', 'Image Reference'),
        ('boolean', 'Boolean Value'),
        ('json', 'JSON Data'),
    ])
    description = models.TextField(blank=True)
    is_sensitive = models.BooleanField(default=False)
    requires_restart = models.BooleanField(default=False)
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.key}: {self.value if not self.is_sensitive else '****'}"
    
    @property
    def blob_folder(self):
        """Returns the setting's blob storage folder path"""
        return f"settings/{self.key}/"

    def upload_file(self, file, filename=None):
        """
        Upload a file to the setting's folder in Azure Blob Storage
        """
        azure_storage = AzureBlobStorage()
        if filename is None:
            filename = file.name
        
        blob_name = f"{self.blob_folder}{filename}"
        return azure_storage.upload_file(file, blob_name)

    def delete_file(self, filename):
        """
        Delete a file from the setting's folder
        """
        azure_storage = AzureBlobStorage()
        blob_name = f"{self.blob_folder}{filename}"
        return azure_storage.delete_file(blob_name)

    def list_files(self):
        """
        List all files in the setting's folder
        """
        azure_storage = AzureBlobStorage()
        return azure_storage.list_files(self.blob_folder)
    
    def get_json_value(self):
        """Return the value as a Python object if it's JSON, otherwise return the raw value"""
        if self.value_type == 'json':
            try:
                return json.loads(self.value)
            except json.JSONDecodeError:
                return {}
        return self.value
    
    def set_json_value(self, data):
        """Set the value from a Python object, converting to JSON string"""
        self.value = json.dumps(data)
        self.value_type = 'json'

class PendingSettingChange(models.Model):
    setting_key = models.CharField(max_length=100)
    new_value = models.TextField()
    requested_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    requested_on = models.DateTimeField(auto_now_add=True)
    applied = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Change to {self.setting_key} (Applied: {self.applied})"
    
class MessageTemplate(models.Model):
    # Basic template information
    # id = models.AutoField(primary_key=True)
    # sentence = models.TextField(help_text="The main message text/sentence")
    sentence = models.JSONField(
        default=list,
        help_text="List of message sentences/texts",
        blank=True
    )
    label = models.CharField(max_length=255, help_text="Display intent for the template")
    parent = models.CharField(max_length=255, help_text="Parent template label for hierarchical organization", null=True, blank=True) # Should this be FK ?
    status = models.CharField(max_length=100, help_text="Status for chatbot", blank=True, null=True)
    department = models.JSONField(default=list, help_text="Which department will be transfered", blank=True)

    section = models.CharField(
        max_length=255, 
        null=True, 
        blank=True,
        help_text="Section grouping for the template"
    )
    data = models.CharField(
        max_length=500, 
        null=True, 
        blank=True,
        help_text="Additional data/parameters for the template"
    )
    
    message_type_text = models.TextField(
        null=True, 
        blank=True,
        help_text="Plain text message content. Use this for simple text-based messages."
    )

    message_type_quick_reply = models.JSONField(
        default=list, 
        blank=True,
        help_text="Array of quick reply buttons. Sample: ['Yes', 'No', 'Confirm', 'Cancel']"
    )

    message_type_image_map = models.JSONField(
        default=dict, 
        blank=True,
        help_text="Platform-specific image map configurations. Format: {'line': {...}, 'facebook': {...}, 'instagram': {...}}"
    )

    message_type_image_carousel = models.JSONField(
        default=dict, 
        blank=True,
        help_text="Platform-specific image carousel configurations. Format: {'line':  {...}]}, 'facebook': {...}, 'instagram': {...}}"
    )

    message_type_carousel = models.JSONField(
        default=dict, 
        blank=True,
        help_text="Platform-specific card carousel configurations. Format: {'line':  {...}, 'facebook': {...}, 'instagram': {...}}"
    )

    message_type_confirm_template = models.JSONField(
        default=dict, 
        blank=True,
        help_text="Platform-specific confirm template configurations. Format: {'line':  {...}, 'facebook': {...}, 'instagram': {...}}"
    )

    message_type_buttons_template = models.JSONField(
        default=dict, 
        blank=True,
        help_text="Platform-specific buttons template configurations. Format: {'line':  {...}, 'facebook': {...}, 'instagram': {...}}"
    )

    @property
    def message_type(self):
        return {
            "text": self.message_type_text,
            "quick_reply": self.message_type_quick_reply,
            "image_map": self.message_type_image_map,
            "image_carousel": self.message_type_image_carousel,
            "carousel": self.message_type_carousel,
            "confirm_template": self.message_type_confirm_template,
            "buttons_template": self.message_type_buttons_template,
        }
    
    # Metadata fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='created_message_templates'
    )
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True,
        related_name='updated_message_templates'
    )
    is_active = models.BooleanField(default=True, help_text="Whether this template is active")
 
    # def __str__(self):
    #     return f"ID: {self.id} | {self.sentence} (sentence) -> {self.label} (label)"
    
    def __str__(self):
        # # Show count of sentences
        # sentence_count = len(self.sentence) if isinstance(self.sentence, list) else 0
        # return f"ID: {self.id} | {sentence_count} sentences -> {self.label} (label)"
    
        sentence_count = len(self.sentence) if isinstance(self.sentence, list) else 0
        section_info = f" | Section: {self.section}" if self.section else ""
        parent_info = f" | Parent: {self.parent}" if self.parent else ""
        status_info = f" | Status: {self.status}" if self.status else ""
        department_info = f" | Department: {self.department}" if self.department else ""
        # return f"ID: {self.id} | {sentence_count} sentences -> {self.label} (label){section_info}"
        # return f"ID: {self.id}{parent_info} | {sentence_count} sentences -> {self.label} (label){section_info} | Status: {self.status} | Departments: {', '.join(self.department) if self.department else 'None'}"
        return f"ID: {self.id}{parent_info} | {sentence_count} sentences -> {self.label} (label){section_info}{status_info}{department_info}"
    
    @property
    def sentence_count(self):
        """Return the number of sentences in the template"""
        return len(self.sentence) if isinstance(self.sentence, list) else 0
    
    @property
    def blob_folder(self):
        """Returns the message_type's blob storage folder path"""
        return f"message_type/{self.id}/"

    def upload_file(self, file, filename=None):
        """Upload a file to the message_type's folder in Azure Blob Storage"""
        azure_storage = AzureBlobStorage()
        if filename is None:
            filename = file.name
        
        blob_name = f"{self.blob_folder}{filename}"
        return azure_storage.upload_file(file, blob_name)

    def delete_file(self, filename):
        """Delete a file from the message_type's folder"""
        azure_storage = AzureBlobStorage()
        blob_name = f"{self.blob_folder}{filename}"
        return azure_storage.delete_file(blob_name)

    def list_files(self):
        """List all files in the message_type's folder"""
        azure_storage = AzureBlobStorage()
        return azure_storage.list_files(self.blob_folder)

    # class Meta:
    #     db_table = 'setting_messagetemplate'
    #     verbose_name = 'Message Template'
    #     verbose_name_plural = 'Message Templates'
    #     ordering = ['section', 'label']  # Order by section first, then label
    
class SLA(models.Model):
    name = models.CharField(max_length=100, unique=True, verbose_name="name")
    value = models.CharField(max_length=50, verbose_name="value")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'sla_table'
        verbose_name = 'SLA'
        verbose_name_plural = 'SLAs'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name}: {self.value}"