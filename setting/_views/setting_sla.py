import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.authentication import JWTAuthentication
# from rest_framework.permissions import IsAdminUser
from setting.models import MessageTemplate, SystemSettings, SLA
from setting.serializers import SLASerializer
from setting.utils import refresh_image_sas_tokens
from user.permissions import IsSupervisorOrHigher
from django.conf import settings as django_settings

from devproject.utils.utils import bcolors
from datetime import datetime, timedelta
from rest_framework.permissions import IsAuthenticated

from django.contrib.auth import get_user_model

User = get_user_model()
logger = logging.getLogger('django.api_logs')

class SLAViewSet(APIView):
    queryset = SLA.objects.all()
    serializer_class = SLASerializer