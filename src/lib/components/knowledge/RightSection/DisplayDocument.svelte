<!-- DisplayDocument.svelte -->
<script lang="ts">
    import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';
	import {formatTimestamp, formatTimestampDMY } from '$lib/utils';

	const lang = get(language);

	import {
		Heading,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Checkbox,
		Button,
		Input,
		Dropdown,
		DropdownItem,
		Datepicker,
		Select,
		MultiSelect,
		Label
	} from 'flowbite-svelte';
	import {
		DownloadOutline,
		TrashBinSolid,
		SearchOutline,
		ChevronDownOutline,
		AdjustmentsHorizontalSolid,
		FileLinesSolid,
	} from 'flowbite-svelte-icons';
	import EditModal from './EditModal.svelte';
	import DeleteModal from './DeleteModal.svelte';
    import Pagination from '$src/lib/components/UI/pagination.svelte';

    import { getBackendUrl } from '$src/lib/config';

	export let documents = [];
	export let documentCat = '';
	export let user_role = 'Agent';


	// Sorting state
    let sortColumn = '';
    let sortDirection = 'asc';

	let sortOrder = 'ascending';
	let fieldSort = 'date_upload';
	
	const noDocColSpan = documentCat === 'Customer Support' ? 3 : 5;

	export let access_token = '';

	let searchQuery = '';
	let isActive = true; // Internal state for filtering

	// ============== FILTER VARIABLES ==============
	let selectedCategories = new Set(['All']);
	let selectedCompanies = new Set(['All']);
	let selectedAccessLevels = new Set(['All']);

	// Available options
	const categoryOptions = ['All', 'CUSTOMER_SUPPORT', 'PROMOTION', 'PRODUCT'];
	const accessLevelOptions = ['All', 'customer', 'admin', 'supervisor', 'agent'];
	
	$: displayDocs = (documents ?? [])
		.filter((document) => document.is_active === isActive)
		.filter((doc) => doc.filename.toLowerCase().includes(searchQuery.toLowerCase()))
		.filter((doc) => {
			if (selectedCategories.has('All')) return true;
			return selectedCategories.has(doc.category);
		})
		.filter((doc) => {
			if (selectedAccessLevels.has('All')) return true;
			// Check if any of the document's access levels match the selected filters
			return doc.access_level.some(level => selectedAccessLevels.has(level));
		});

	let editModal = false;
	let deleteModal = false;
	let modalDocument = null;

	let selectedDocuments = new Set();
	let selectAll = false;

	// pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;

	$: totalPages = Math.ceil(Math.max((displayDocs ?? []).length, 1) / itemsPerPage);
	$: paginatedDocuments = (displayDocs ?? []).slice(0, itemsPerPage);

	function toggleSelection(docId: string) {
		selectedDocuments = new Set(selectedDocuments);
		selectedDocuments.has(docId) ? selectedDocuments.delete(docId) : selectedDocuments.add(docId);

		if (selectedDocuments.size === paginatedDocuments.length) {
			selectAll = true;
		} else if (selectedDocuments.size === 0) {
			selectAll = false;
		}
	}

	function toggleSelectAll() {
		selectAll
			? (selectedDocuments = new Set())
			: (selectedDocuments = new Set(paginatedDocuments.map((doc) => doc.id)));

		selectAll = !selectAll;
	}

	const displayDate = (timestamp) => {
		const displayCreated = new Date(timestamp);

		return displayCreated.toLocaleDateString('en-GB', {
			day: '2-digit',
			month: 'short',
			year: 'numeric'
		});
	};

	function downloadSelected() {
        const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
		const url = `${backendUrl.replace(/\/$/, '')}/llm_rag_doc/azure/blob/files/batch-download/`;
		const document_ids = Array.from(selectedDocuments);
		const bodyData = { document_ids };

		fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Content-Type': 'application/json' // Ensure JSON request
			},
			body: JSON.stringify(bodyData)
		})
			.then((response) => response.blob())
			.then((blob) => {
				const link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.click();
			})
			.catch((error) => {
				console.error('Error downloading the file:', error);
			});
	}

	// Function to toggle edit modal
	function openEditModal(document) {
		editModal = true;
		modalDocument = { ...document }; // Create a new copy of the document to ensure reinitialization
	}

	function closeEditModal() {
		editModal = false;
	}

	function openDeletelModal() {
		deleteModal = true;
	}

	function closeDeleteModal() {
		deleteModal = false;
	}

	// Pagination Functions
	function updatePagination() {
		const idx = (currentPage - 1) * itemsPerPage;
		paginatedDocuments = displayDocs.slice(idx, Math.min(idx + itemsPerPage, displayDocs.length));

		selectedDocuments = new Set();
		selectAll = false;
	}

	let addedFilters: String[] = [];
	let availableFilters = {
		after_start_date_upload: true,
		before_end_date_upload: true,
		after_promo_start_date: true,
		before_promo_end_date: true,
		product_type: true
	};

	let filterValues = {
		after_start_date_upload: null,
		before_end_date_upload: null,
		after_promo_start_date: null,
		before_promo_end_date: null,
		product_type: null
	};

	let productTypeSelectFilter = [];
	let productTypes = [
		{ value: 'CAR', name: 'CAR' },
		{ value: 'COMPULSORY_MOTOR', name: 'COMPULSORY_MOTOR' },
		{ value: 'HEALTH_ACCIDENT_TRAVEL', name: 'HEALTH_ACCIDENT_TRAVEL' },
		{ value: 'BUSINESS', name: 'BUSINESS' },
		{ value: 'HOME', name: 'HOME' },
		{ value: 'SHIPPING', name: 'SHIPPING' },
		{ value: 'CANCER', name: 'CANCER' },
		{ value: 'CYBER', name: 'CYBER' }
	];

    function updateCurrentPage(newCurrentPage: number) {
        currentPage = newCurrentPage;
        updatePagination();
    }

	function resetFilters() {
		selectedCategories = new Set(['All']);
		selectedCompanies = new Set(['All']);
		selectedAccessLevels = new Set(['All']);
		isActive = true;
	}

	// Function to toggle between active and inactive documents
	function toggleActiveFilter(activeState: boolean) {
		isActive = activeState;
		currentPage = 1;
		selectedDocuments = new Set();
		selectAll = false;
	}

	// ============== FILTER FUNCTIONS ==============
	function toggleCategory(status: string) {
		const newSet = new Set(selectedCategories);

		if (status === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(status)) {
				newSet.delete(status);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(status);
			}
		}

		selectedCategories = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleAccessLevel(accessLevel: string) {
		const newSet = new Set(selectedAccessLevels);

		if (accessLevel === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(accessLevel)) {
				newSet.delete(accessLevel);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(accessLevel);
			}
		}

		selectedAccessLevels = newSet;
		currentPage = 1;
	}
</script>

<!-- Filter Tab -->
<div class="mt-4 mb-4 grid grid-cols-1 gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
	<!-- Left side - Filter Buttons -->
	<div class="col-span-2">
		<div id="users-page-filter-buttons" class="flex flex-wrap gap-3">
			<!-- Active/Inactive Document Filter -->
			<Button
				color={isActive ? 'dark' : 'none'}
				class={`${isActive ? '' : 'border hover:bg-gray-100'} shadow-md`}
				on:click={() => toggleActiveFilter(true)}
			>
				<FileLinesSolid class="h-5 w-5" /> {t('all_documents')} ({(documents ?? []).filter(doc => doc.is_active === true).length})
			</Button>

			<Button
				color={!isActive ? 'dark' : 'none'}
				class={`${!isActive ? '' : 'border hover:bg-gray-100'} shadow-md`}
				on:click={() => toggleActiveFilter(false)}
			>
				<TrashBinSolid class="h-5 w-5" /> {t('trash')} ({(documents ?? []).filter(doc => doc.is_active === false).length})
			</Button>

			<!-- Category Filter -->
			<div>
				<Button
					id="users-page-category-filter-button"
					color={!selectedCategories.has('All') ? 'dark' : 'none'}
					class={`${!selectedCategories.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
				>
					<AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
					<span>{t('filter_category')}</span>
					<ChevronDownOutline class="h-3 w-3" />
				</Button>
				<Dropdown id="users-page-category-filter-dropdown" class="w-44 p-2 shadow-lg">
					{#each categoryOptions as category}
						<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
							<Checkbox
								id="users-page-category-filter-{category.toLowerCase()}"
								checked={selectedCategories.has(category)}
								on:change={() => toggleCategory(category)}
								class="text-gray-700 focus:ring-gray-700 p-2"
								inline
							/>
								<span class="ml-2 text-sm">
									{category === 'All'
										? t('filter_all')
										: t(category.toLowerCase())
									}
								</span>
						</Label>
					{/each}
				</Dropdown>
			</div>

			<!-- Access Level Filter -->
			<div>
				<Button
					id="users-page-access-level-filter-button"
					color={!selectedAccessLevels.has('All') ? 'dark' : 'none'}
					class={`${!selectedAccessLevels.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
				>
					<AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
					<span>{t('filter_access_level')}</span>
					<ChevronDownOutline class="h-3 w-3" />
				</Button>
				<Dropdown id="users-page-access-level-filter-dropdown" class="w-44 p-2 shadow-lg">
					{#each accessLevelOptions as accessLevel}
						<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
							<Checkbox
								id="users-page-access-level-filter-{accessLevel.toLowerCase()}"
								checked={selectedAccessLevels.has(accessLevel)}
								on:change={() => toggleAccessLevel(accessLevel)}
								class="text-gray-700 focus:ring-gray-700 p-2"
								inline
							/>
								<span class="ml-2 text-sm">
									{accessLevel === 'All'
										? t('filter_all')
										: t(accessLevel.toLowerCase())
									}
								</span>
						</Label>
					{/each}
				</Dropdown>
			</div>

			<!-- Reset Filter -->
			<Button
				id="users-page-reset-filters-button"
				color="none"
				on:click={resetFilters}
				class="w-auto border shadow-md hover:bg-gray-100"
			>
				{t('filter_reset')}
			</Button>
		</div>
	</div>

	<!-- Right side - Search Bar -->
	<div class="col-span-1">
		<div id="users-page-search-container" class="relative w-full shadow-md">
			<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
				<SearchOutline class="h-5 w-5 text-gray-500" />
			</div>
			<Input
				type="text"
				id="knowledge-base-search-input"
				placeholder={t('search_filename')}
				bind:value={searchQuery}
				class={`block w-full rounded-lg border bg-white py-2.5 pl-10 focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700 ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
			/>
		</div>
	</div>
</div>

<!-- Selection Tab -->
<div class="mb-4 flex gap-8 p-4">
	<div class="flex items-center gap-4">
		<Checkbox
			checked={selectedDocuments.size === paginatedDocuments.length &&
				paginatedDocuments.length > 0}
			on:change={toggleSelectAll}
			indeterminate={selectedDocuments.size > 0 &&
				selectedDocuments.size < paginatedDocuments.length}
		/>
		<p class="font-semibold text-gray-700">{selectedDocuments.size} {t('selected')}</p>
	</div>
	<div class="flex gap-1">
		{#if isActive}
			<Button
				color="none"
				on:click={downloadSelected}
				disabled={selectedDocuments.size === 0}
				class="hover:bg-gray-100"
			>
				<DownloadOutline /> {t('download_file')}
			</Button>
			{#if user_role !== 'Agent'}
				<Button
					color="none"
					on:click={openDeletelModal}
					disabled={selectedDocuments.size === 0}
					class="hover:bg-gray-100"
				>
					<TrashBinSolid /> {t('delete')}
				</Button>
			{/if}
		{:else}
			<Button
				color="none"
				on:click={downloadSelected}
				disabled={selectedDocuments.size === 0}
				class="hover:bg-gray-100"
			>
				<DownloadOutline /> {t('download_file')}
			</Button>
			{#if user_role !== 'Agent'}
				<Button
					color="none"
					class="hover:bg-gray-100"
					on:click={openDeletelModal}
					disabled={selectedDocuments.size === 0}
				>
					<TrashBinSolid /> {t('delete_permanently')}
				</Button>
			{/if}
			<!-- <Button color="none" class="hover:bg-gray-100">
				<ArrowsRepeatOutline /> Recover
			</Button> -->
		{/if}
	</div>
</div>

<!-- Table List table-fixed --> 
<Table class="w-full"> 
	<TableHead>
		<TableHeadCell class="p-4! w-12"></TableHeadCell>
		<!-- <TableHeadCell class="w-16 cursor-pointer" on:click={() => sortTable('id')}>
			<div class="flex items-center justify-center">
				{t('table_no')}
				{#if sortColumn === 'id'}
					{#if sortDirection === 'desc'}
						<CaretDownSolid class="ml-1 h-4 w-4" />
					{:else}
						<CaretUpSolid class="ml-1 h-4 w-4" />
					{/if}
				{/if}
			</div>
		</TableHeadCell> -->
		<TableHeadCell class="w-48 whitespace-normal break-words">{t('filename')}</TableHeadCell>
		<TableHeadCell class="w-36 whitespace-normal break-words">{t('file_type')}</TableHeadCell>
		<TableHeadCell class="w-36 whitespace-normal break-words">{t('access_permissions')}</TableHeadCell>
		<TableHeadCell class="w-60">{t('description')}</TableHeadCell>
		<TableHeadCell class="w-32">{t('upload_by')}</TableHeadCell>
		<TableHeadCell class="w-32">{t('table_time')}</TableHeadCell>
	</TableHead>
	<TableBody tableBodyClass="divide-y">
		{#if paginatedDocuments.length === 0}
			<TableBodyRow>
				<TableBodyCell colspan={noDocColSpan} class="py-4 text-center text-gray-500">
					{t('no_documents')}
				</TableBodyCell>
			</TableBodyRow>
		{:else}
			{#each paginatedDocuments as document}
				{#if isActive === document.is_active}
					<TableBodyRow class={selectedDocuments.has(document.id) ? 'bg-gray-100' : ''}>
						<TableBodyCell class="p-4! w-12">
							<Checkbox
								checked={selectedDocuments.has(document.id)}
								on:change={() => toggleSelection(document.id)}
							/>
						</TableBodyCell>
						<!-- <TableBodyCell class="text-center text-sm font-medium text-gray-500">
							{document.id}
						</TableBodyCell> -->
						<TableBodyCell class="w-48 whitespace-normal break-words">
							{document.filename}
							{#if document.category === 'PRODUCT'}
								<span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700">
									{t('pairs_with')} {document.image_document.filename}
								</span>
							{/if}
						</TableBodyCell>
						<TableBodyCell class="w-48 whitespace-normal break-words">
							{document.category}
						</TableBodyCell>
						<TableBodyCell class="w-60 whitespace-normal break-words">
							<div class="flex flex-wrap gap-1">
								{#each document.access_level as permission}
									<span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700">
										{t(permission)}
									</span>
								{/each}
                        	</div>
						</TableBodyCell>
						<TableBodyCell class="w-60 whitespace-normal break-words">
							{document.description}
						</TableBodyCell>
						<TableBodyCell class="w-60 whitespace-normal break-words">
							{document.created_by}
						</TableBodyCell>
						<TableBodyCell class="w-60 whitespace-normal break-words">
							{formatTimestamp(document.created_on)}
						</TableBodyCell>
					</TableBodyRow>
				{/if}
			{/each}
		{/if}
	</TableBody>
</Table>

<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
<EditModal bind:editModal on:close={closeEditModal} {documentCat} document={modalDocument} />
<DeleteModal bind:deleteModal {selectedDocuments} {isActive} on:close={closeDeleteModal} />

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>